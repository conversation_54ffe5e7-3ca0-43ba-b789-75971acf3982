using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Xml;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 盘点结果回调上报接口 【iWMS提供，SSWMS调用】
    /// 注册(regist) -> 获取凭证(applyToken) -> 携带凭证调用 accessInterface(InventoryResultCallback)
    /// </summary>
    public class InventoryResultCallback : InterfaceBase
    {
        // 文档中的通用响应结构
        class AuthResponse
        {
            public int status { get; set; }
            public string message { get; set; }
            public string data { get; set; }
        }
        /// <summary>
        /// 访问接口的Response结构
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        // 盘点结果-物资明细
        public class StockTakeResultGoodsItem
        {
            public string inventoryId { get; set; }
            public string stockTakeTaskId { get; set; }
            public string stockTakeTaskName { get; set; }
            public string stockTakeTaskCode { get; set; }
            public string stockTakeResultId { get; set; }
            public string goodsId { get; set; }
            public string goodsCode { get; set; }
            public string goodsName { get; set; }
            public string goodsVersion { get; set; }
            public int storageNum { get; set; }
            public int lockNum { get; set; }
            public int stockTakeNum { get; set; }
            public int stockTakeOverNum { get; set; }
            public int stockTakeFloorNum { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string brand { get; set; }
            public string warehouseId { get; set; }
            public string warehouseName { get; set; }
            public string warehouseCode { get; set; }
            public string shelfId { get; set; }
            public string shelfName { get; set; }
            public string shelfCode { get; set; }
            public string stockTakeUserId { get; set; }
            public string stockTakeUserName { get; set; }
            public string stockTakePlanStartDate { get; set; }
            public string stockTakePlanEndDate { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        // 最终上报负载
        class InventoryResultPayload
        {
            public string stockTakeId { get; set; }
            public string stockTakeName { get; set; }
            public string stockTakeCode { get; set; }
            public List<StockTakeResultGoodsItem> stockTakeResultGoodsList { get; set; }
        }

        /// <summary>
        /// 调用外部物资系统盘点结果回调。
        /// </summary>
        /// <param name="stockTakeId">盘点计划id</param>
        /// <param name="stockTakeName">盘点计划名称</param>
        /// <param name="stockTakeCode">盘点计划单编号</param>
        /// <param name="stockTakeResultGoodsList">盘点结果明细</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(string stockTakeId, string stockTakeName, string stockTakeCode,
            List<StockTakeResultGoodsItem> stockTakeResultGoodsList, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 基础校验
                if (string.IsNullOrEmpty(stockTakeId) || string.IsNullOrEmpty(stockTakeCode))
                {
                    result = false;
                    message = "参数错误：stockTakeId/stockTakeCode 不能为空";
                    return result;
                }
                if (stockTakeResultGoodsList == null || stockTakeResultGoodsList.Count == 0)
                {
                    result = false;
                    message = "参数错误：stockTakeResultGoodsList 不能为空";
                    return result;
                }

                // Step 1: 注册，获取secrit
                string registInput = Common.JsonHelper.Serializer(new { systemIdentify = "ZYK" });
                if (!InvokeExternal("regist", registInput, out string registOut))
                {
                    result = false;
                    message = $"调用regist失败：{registOut}";
                    return result;
                }

                //测试数据
                //registOut = "{\n\"data\":\"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZnA8edAy1OiuYeRk5CSYJIEpljD12MKM5o8JkBOhf+xDEw7YU2AhhRBTXkjyU04msgyOu8WCiXnx8Wr15ZzV7A1BT5ymQOQgzsJUVQCj0FGtp9IqTdHcV2CQPY7d2/YWcYylcMk54COpikUY5w3gzFGlGqIJLOBTT/mboYn3aTwIDAQAB\",\n\"message\":\"成功\",\n\"status\":200}\n";

                var registResp = TryParse<AuthResponse>(registOut, out string parseErr1);
                if (registResp == null || registResp.status != 0 || string.IsNullOrEmpty(registResp.data))
                {
                    result = false;
                    message = $"regist返回无效：{(registResp == null ? parseErr1 : registResp.message)}";
                    return result;
                }

                string secrit = registResp.data;

                // Step 2: 获取凭证 certificate
                var applyTokenInputObj = new
                {
                    interfaceIdentify = "InventoryResultCallback",
                    publicKey = secrit,
                    systemIdentify = "ZYK",
                    tokenExpired = "2"
                };
                string applyTokenInput = Common.JsonHelper.Serializer(applyTokenInputObj);
                if (!InvokeExternal("getCertificate", applyTokenInput, out string applyTokenOut))
                {
                    result = false;
                    message = $"调用getCertificate失败：{applyTokenOut}";
                    return result;
                }

                //测试数据
                //applyTokenOut = "{\"data\":\"J3WDMrwrmZdNmiLSZfcmBVHEyUGdVKBkGJsm2fZifYP/U5ihJnhhuF9JB6kyfGrn+M93oF8iz3CuD1lg9sp606OroL/D6PWBGxek2pMehGmjcy79b4LWwwJNNVd2TVYnEi7w2NOUUaG19B5oe9vZ/ODewJE0yehiNnLVkIcELy0=\",\"message\":\"成功\",\"status\":200}";

                var applyResp = TryParse<AuthResponse>(applyTokenOut, out string parseErr2);
                if (applyResp == null || applyResp.status != 0 || string.IsNullOrEmpty(applyResp.data))
                {
                    result = false;
                    message = $"getCertificate返回无效：{(applyResp == null ? parseErr2 : applyResp.message)}";
                    return result;
                }

                string certificate = applyResp.data;

                // Step 3: 组织业务负载并调用 accessInterface(InventoryResultCallback)
                var payload = new InventoryResultPayload
                {
                    stockTakeId = stockTakeId,
                    stockTakeName = stockTakeName,
                    stockTakeCode = stockTakeCode,
                    stockTakeResultGoodsList = stockTakeResultGoodsList
                };
                string payloadJson = Common.JsonHelper.Serializer(payload);

                // 按文档要求，accessInterface 的 paramsJSONStr 结构，包含certificate
                string paramsJSONStr = Common.JsonHelper.Serializer(new
                {
                    systemIdentify = "ZYK",
                    certificate = certificate,
                    json = payloadJson
                });

                // 由于accessInterface需要在SOAP Header中携带certificate，
                // 这里直接构造SOAP并发送
                if (!PostAccessInterfaceWithCertificate("accessInterface", certificate, paramsJSONStr, out string accessRespJson, out string httpErr))
                {
                    result = false;
                    message = $"调用accessInterface失败：{httpErr}";
                    return result;
                }

                //测试数据
                //accessRespJson = "{\n  \"code\": 0,\n  \"msg\": \"msg_60299ecfb046\",\n  \"traceId\": \"traceId_9d61457be7b5\"\n}\n";

                // 按文档，该接口通常返回形如 {"status":200,"message":"成功","data":"..."}
                var accessResp = TryParse<OutputParam>(accessRespJson, out string parseErr3);
                if (accessResp == null || (accessResp.code != 0 && accessResp.code != 1 && accessResp.code != 2))
                {
                    result = false;
                    message = $"accessInterface返回无效：{(accessResp == null ? parseErr3 : accessResp.msg)}";
                    return result;
                }

                message = "盘点结果上报成功";
                S_Base.sBase.Log.Info($"InventoryResultCallback成功_计划[{stockTakeCode}]_明细数[{stockTakeResultGoodsList.Count}]_traceId[{Guid.NewGuid()}]_信息[{message}]");
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
                S_Base.sBase.Log.Error($"InventoryResultCallback异常：{ex.Message}", ex);
            }

            return result;
        }

        private T TryParse<T>(string jsonOrWrapped, out string error) where T : class
        {
            error = null;
            try
            {
                // 某些WebService返回SOAP中包含<String>{json}</String>，
                // 若传入已是纯json字符串也可直接解析
                string candidate = jsonOrWrapped;
                // 提取可能包裹的XML中的<String>内容
                if (!string.IsNullOrEmpty(candidate) && candidate.Contains("<"))
                {
                    try
                    {
                        var xml = new XmlDocument();
                        xml.LoadXml(candidate);
                        var node = xml.SelectSingleNode("//String");
                        if (node != null)
                        {
                            candidate = node.InnerText;
                        }
                    }
                    catch { /* ignore xml parse */ }
                }
                return Common.JsonHelper.Deserialize<T>(candidate);
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return null;
            }
        }

        private bool PostAccessInterfaceWithCertificate(string methodName, string certificate, string paramsJSONStr, out string responseJson, out string error)
        {
            responseJson = string.Empty;
            error = null;
            try
            {
                // 使用InterfaceBase中配置的ExternalServiceUrl
                string serviceUrl = GetExternalServiceUrl();
                if (string.IsNullOrEmpty(serviceUrl))
                {
                    error = "未配置ExternalServiceUrl";
                    return false;
                }

                // 构建SOAP Envelope，根据WSDL规范
                var sb = new StringBuilder();
                sb.Append("<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tns=\"http://ws.com/\">");
                sb.Append("<soap:Header>");
                // 根据WSDL，certificate应该作为参数传递，而不是在Header中
                // 如果确实需要在Header中传递，需要确认正确的命名空间
                sb.Append("</soap:Header>");
                sb.Append("<soap:Body>");
                sb.Append("<tns:").Append(methodName).Append(">");
                sb.Append("<tns:paramsJSONStr>").Append(System.Security.SecurityElement.Escape(paramsJSONStr)).Append("</tns:paramsJSONStr>");
                sb.Append("</tns:").Append(methodName).Append(">");
                sb.Append("</soap:Body>");
                sb.Append("</soap:Envelope>");

                var req = (HttpWebRequest)WebRequest.Create(serviceUrl);
                req.Method = "POST";
                req.ContentType = "text/xml; charset=utf-8";
                // 根据WSDL，SOAPAction应该为空字符串
                req.Headers.Add("SOAPAction", "\"\"");

                byte[] data = Encoding.UTF8.GetBytes(sb.ToString());
                using (var stream = req.GetRequestStream())
                {
                    stream.Write(data, 0, data.Length);
                }

                using (var resp = (HttpWebResponse)req.GetResponse())
                using (var reader = new System.IO.StreamReader(resp.GetResponseStream(), Encoding.UTF8))
                {
                    string respText = reader.ReadToEnd();
                    // 根据WSDL，响应格式应该是 accessInterfaceResponse/String
                    var xml = new XmlDocument();
                    xml.LoadXml(respText);

                    // 创建命名空间管理器
                    var nsmgr = new XmlNamespaceManager(xml.NameTable);
                    nsmgr.AddNamespace("soap", "http://schemas.xmlsoap.org/soap/envelope/");
                    nsmgr.AddNamespace("tns", "http://ws.com/");

                    // 尝试按WSDL规范查找响应
                    var node = xml.SelectSingleNode("//tns:accessInterfaceResponse/tns:String", nsmgr);
                    if (node == null)
                    {
                        // 兜底：查找任何String节点
                        node = xml.SelectSingleNode("//String");
                    }

                    if (node != null)
                    {
                        responseJson = node.InnerText;
                    }
                    else
                    {
                        // 兜底：直接返回原文
                        responseJson = respText;
                    }
                }

                return true;
            }
            catch (WebException wex)
            {
                error = wex.Message;
                try
                {
                    using (var resp = (HttpWebResponse)wex.Response)
                    using (var reader = new System.IO.StreamReader(resp.GetResponseStream()))
                    {
                        error += " | " + reader.ReadToEnd();
                    }
                }
                catch { }
                return false;
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return false;
            }
        }

        private string GetExternalServiceUrl()
        {
            // InterfaceBase中externalServiceUrl是private静态，这里从配置读取同名键
            return SiaSun.LMS.Common.StringUtil.GetConfig("ExternalServiceUrl");
        }
    }
}

